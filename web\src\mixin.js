export const utils = {
  methods: {
    getInitializeDate() {
      let date = new Date() //获取新的时间
      //获取当前年份,并且转为字符串
      let year = date.getFullYear().toString()
      //获取当前月份，因为月份是要从0开始，此处要加1，
      //使用三元表达式，判断是否小于10，如果是的话，就在字符串前面拼接'0'
      let month =
        date.getMonth() + 1 < 10
          ? '0' + (date.getMonth() + 1).toString()
          : (date.getMonth() + 1).toString()
      //获取天，判断是否小于10，如果是的话，就在在字符串前面拼接'0'
      let day =
        date.getDate() < 10
          ? '0' + (date.getDate()).toString()
          : (date.getDate()).toString()
      //字符串拼接，将开始时间和结束时间进行拼接
      let beginTime = year + '-' + month + '-01' //当月第一天
      let endTime = year + '-' + month + '-' + day //当天
      this.dateRange = [beginTime, endTime] //将值设置给组件DatePicker 绑定的数据
    },
    //获取当天日期 单日
    getInitializeTodayDate() {
      let date = new Date() //获取新的时间
      //获取当前年份,并且转为字符串
      let year = date.getFullYear().toString()
      //获取当前月份，因为月份是要从0开始，此处要加1，
      //使用三元表达式，判断是否小于10，如果是的话，就在字符串前面拼接'0'
      let month =
        date.getMonth() + 1 < 10
          ? '0' + (date.getMonth() + 1).toString()
          : (date.getMonth() + 1).toString()
      //获取天，判断是否小于10，如果是的话，就在在字符串前面拼接'0'
      let day =
        date.getDate() < 10
          ? '0' + date.getDate().toString()
          : date.getDate().toString()
      //字符串拼接，将开始时间和结束时间进行拼接
      // let beginTime = year + "-" + month + "-01"; //当月第一天
      let endTime = year + '-' + month + '-' + day //当天
      this.dateRange = endTime //将值设置给组件DatePicker 绑定的数据
    },
    //获取当天日期 数组
    getInitializeTodayDateArray() {
      let date = new Date() //获取新的时间
      //获取当前年份,并且转为字符串
      let year = date.getFullYear().toString()
      //获取当前月份，因为月份是要从0开始，此处要加1，
      //使用三元表达式，判断是否小于10，如果是的话，就在字符串前面拼接'0'
      let month =
        date.getMonth() + 1 < 10
          ? '0' + (date.getMonth() + 1).toString()
          : (date.getMonth() + 1).toString()
      //获取天，判断是否小于10，如果是的话，就在在字符串前面拼接'0'
      let day =
        date.getDate() < 10
          ? '0' + date.getDate().toString()
          : date.getDate().toString()
      //字符串拼接，将开始时间和结束时间进行拼接
      // let beginTime = year + "-" + month + "-01"; //当月第一天
      let beginTime = year + '-' + month + '-' + day //当天
      let endTime = year + '-' + month + '-' + day //当天
      this.dateRange = [beginTime, endTime]  //将值设置给组件DatePicker 绑定的数据
    },

    //获取当前年
    getCurrentYear() {
      // 获取当前年份
      this.year = new Date().getFullYear()

    },

    //乘法  ===》处理精度缺失
    accMul(arg1, arg2) {
      var m = 0,
        s1 = arg1.toString(),
        s2 = arg2.toString()
      try {
        m += s1.split('.')[1].length
      } catch (e) {
      }
      try {
        m += s2.split('.')[1].length
      } catch (e) {
      }
      return (
        (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) /
        Math.pow(10, m)
      )
    },
    formatDateMy(dateString) {
      const date = new Date(dateString)
      const year = date.getFullYear()
      const month = ('0' + (date.getMonth() + 1)).slice(-2) // getMonth返回的月份是从0开始的
      const day = ('0' + date.getDate()).slice(-2)
      return `${year}-${month}-${day}`
    },
    loadingMethed() {
      return this.$loading({
        lock: true,
        text: '稍等,数据加载中(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    },
    //通用table表格方法合并
    commonFormMergeMethod(tableData = [], tableColumn = [], mergeCols = []) {
      const fields = tableColumn?.map(v => v.prop)
      const array = []
      if (!tableData?.length || !tableColumn?.length || !mergeCols?.length) return
      // 倒叙遍历行（方便统计合并列单元格数至最上方，避免表格塌陷）
      for (let row = tableData.length - 1; row >= 0; row--) {
        array[row] = []
        for (let col = 0; col < fields.length; col++) {
          // 1.最后一行单元格不合并（初始无可对比数据）
          // 2.不在指定列（mergeCols）的单元格不合并
          // 3.空值不合并
          if (row === tableData.length - 1 || !mergeCols.includes(fields[col]) || !tableData[row][fields[col]]) {
            array[row][col] = [1, 1]
            continue
          }
          // 4.数据相同但所属父级不一致的单元格不合并
          const parentFields = mergeCols.slice(0, col) // 在指定合并列中找出所有父级
          if (mergeCols.includes(fields[col]) && parentFields?.includes(fields[col - 1])) {
            const currentParents = parentFields.map(field => tableData[row][field]) // 当前单元格所有父级
            const nextRowParents = parentFields.map(field => tableData[row + 1][field]) // 下一行单元格所有父级
            if (currentParents?.toString() !== nextRowParents?.toString()) {
              array[row][col] = [1, 1]
              continue
            }
          }      // 5.合并相同数据的单元格
          if (tableData[row][fields[col]] === tableData[row + 1][fields[col]]) {
            const beforeCell = array[row + 1][col]
            array[row][col] = [1 + beforeCell[0], 1]
            beforeCell[0] = 0
            beforeCell[1] = 0
          } else {
            array[row][col] = [1, 1] // 否则不合并
          }
        }
      }
      // console.log(array, 'array')
      return array
    }
  }
}
