<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="选择时间" prop="">
        <el-date-picker
          v-model="queryParams.dateRange"
          type="daterange"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-search"
          @click="handleQuery"
        >上传
        </el-button>
      </el-form-item>
    </el-form>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <span>这是一段信息</span>
      <span slot="footer" class="dialog-footer">
    <el-button @click="dialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
  </span>
    </el-dialog>
  </div>
</template>

<script>
import { utils } from '@/mixin.js'

export default {
  name: 'index',
  mixins: [utils],
  components: {},
  props: {},
  data() {
    return {
      dialogVisible: false,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dateRange: []
      }
    }
  },
  methods: {
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    getList() {
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done()
        })
        .catch(_ => {
        })
    }
  },
  computed: {},
  watch: {},

  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    this.getList()
  },
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
  }
}
</script>

<style lang="scss" scoped>
.index {
}
</style>

