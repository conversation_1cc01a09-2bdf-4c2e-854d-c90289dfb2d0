<template>
  <div class="week-home">
    <div :style="myStyle.title" >
      {{this.title}}
    </div>
    <div :style="myStyle.height" class="table-d">
      <table>
        <!-- 设置列宽 -->
        <colgroup>
          <col width="5%">
          <col width="13%">
          <col width="17%">
          <col width="35%">
          <col width="30%">
          <col/>
        </colgroup>
        <thead>
        <th>序号</th>
        <th>交班日期</th>
        <th>标题</th>
        <th>值班内容</th>
        <th>处理结果</th>
        </thead>
      </table>
      <vue-seamless-scroll :data="tableData" :class-option="defaultOption" class="seamless-warp"
                           v-if="tableData[0]" :style="myStyle.tableHeight">
        <ul>
          <li v-for="(item,index) in tableData" class="liStyle">
            <span class="title1 text_align">{{ item.event_Number }}</span>
            <span class="title2 text_align">{{ item.duty_Date }}</span>
            <span class="title3 text_align">{{ item.event_Title }}</span>
            <span class="title4 text_align">{{ item.event_Content }}</span>
            <span class="title5 text_align">{{ item.result }}</span>
          </li>
        </ul>
      </vue-seamless-scroll>
    </div>
  </div>
</template>

<script>
import {GetDutyRoomWeeklyShift} from '@/api/shiftReport/statement'
import vueSeamlessScroll from "vue-seamless-scroll";
export default {
  name: 'shiftRecordsWeekContent',
  props: ['title','myStyle'],
  components: {vueSeamlessScroll},
  computed: {
    defaultOption() {
      return {
        step: 0.1, // 数值越大速度滚动越快
        limitMoveNum: this.tableData.length, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },
  data() {
    return {
      tableData: [],
      rowIndex: 0, // 当前滚动到的行索引
      intervalId: null // 定时器ID
    }
  },
  created() {
    this.getData()
  },
  mounted() {
  },
  methods: {
    getData(){
      GetDutyRoomWeeklyShift().then(res => {
        this.tableData = res.data;
      })
    },
  },
}
</script>

<style scoped lang="scss">
.week-home{
  color: #FFFFFF;
  * {
    margin: 0;
    padding: 0;
    /*font-weight: normal;*/
  }

  ul {
    list-style: none;
  }

  table {
    width: 100%;
    text-align: center;
  }

  th {
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    background-color: rgba(180, 181, 198, 0.1);
  }

  .seamless-warp {
    height: 150px;
    overflow: hidden;
    .liStyle {
      height: 50px;
      line-height: 30px;
      font-size: 26px;
      width: 100%;
      display: flex; //设置flex布局，否则span标签无法设置宽度
      //设置宽度与表头列宽一致
      .title1 { width: 5%; }

      .title2 { width: 13%; }

      .title3 { width: 17%; }

      .title4 { width: 35%; }

      .title5 { width: 30%; }

      .text_align { text-align: center; white-space: nowrap;overflow: hidden;text-overflow: ellipsis; }
    }
  }
  border-bottom:2px solid #262D5B;
}
</style>
