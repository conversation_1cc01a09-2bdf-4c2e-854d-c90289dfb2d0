<template>
  <div class="table-home">

    <div class="table-master">
      <el-table :data="responseData.itemList" :height="myStyle.tableHeight" border style="width: 100%">
        <el-table-column fixed="left" prop="deptName" align="center" label="科室" width="150"></el-table-column>
        <el-table-column label="一月" align="center">
          <el-table-column prop="beginYear01" align="center" :label="responseData.beginYear + '年'"></el-table-column>
          <el-table-column prop="endYear01" align="center" :label="responseData.endYear + '年'"></el-table-column>
          <el-table-column prop="x01" align="center" label="同比"></el-table-column>
        </el-table-column>
        <el-table-column label="二月" align="center">
          <el-table-column prop="beginYear02" align="center" :label="responseData.beginYear + '年'"></el-table-column>
          <el-table-column prop="endYear02" align="center" :label="responseData.endYear + '年'"></el-table-column>
          <el-table-column prop="x02" align="center" label="同比"></el-table-column>
          <el-table-column prop="y02" align="center" label="环比"></el-table-column>
        </el-table-column>
        <el-table-column label="三月" align="center">
          <el-table-column prop="beginYear03" align="center" :label="responseData.beginYear + '年'"></el-table-column>
          <el-table-column prop="endYear03" align="center" :label="responseData.endYear + '年'"></el-table-column>
          <el-table-column prop="x03" align="center" label="同比"></el-table-column>
          <el-table-column prop="y03" align="center" label="环比"></el-table-column>
        </el-table-column>
        <el-table-column label="四月" align="center">
          <el-table-column prop="beginYear04" align="center" :label="responseData.beginYear + '年'"></el-table-column>
          <el-table-column prop="endYear04" align="center" :label="responseData.endYear + '年'"></el-table-column>
          <el-table-column prop="x04" align="center" label="同比"></el-table-column>
          <el-table-column prop="y04" align="center" label="环比"></el-table-column>
        </el-table-column>
        <el-table-column label="五月" align="center">
          <el-table-column prop="beginYear05" align="center" :label="responseData.beginYear + '年'"></el-table-column>
          <el-table-column prop="endYear05" align="center" :label="responseData.endYear + '年'"></el-table-column>
          <el-table-column prop="x05" align="center" label="同比"></el-table-column>
          <el-table-column prop="y05" align="center" label="环比"></el-table-column>
        </el-table-column>
        <el-table-column label="六月" align="center">
          <el-table-column prop="beginYear06" align="center" :label="responseData.beginYear + '年'"></el-table-column>
          <el-table-column prop="endYear06" align="center" :label="responseData.endYear + '年'"></el-table-column>
          <el-table-column prop="x06" align="center" label="同比"></el-table-column>
          <el-table-column prop="y06" align="center" label="环比"></el-table-column>
        </el-table-column>
        <el-table-column label="七月" align="center">
          <el-table-column prop="beginYear07" align="center" :label="responseData.beginYear + '年'"></el-table-column>
          <el-table-column prop="endYear07" align="center" :label="responseData.endYear + '年'"></el-table-column>
          <el-table-column prop="x07" align="center" label="同比"></el-table-column>
          <el-table-column prop="y07" align="center" label="环比"></el-table-column>
        </el-table-column>
        <el-table-column label="八月" align="center">
          <el-table-column prop="beginYear08" align="center" :label="responseData.beginYear + '年'"></el-table-column>
          <el-table-column prop="endYear08" align="center" :label="responseData.endYear + '年'"></el-table-column>
          <el-table-column prop="x08" align="center" label="同比"></el-table-column>
          <el-table-column prop="y08" align="center" label="环比"></el-table-column>
        </el-table-column>
        <el-table-column label="九月" align="center">
          <el-table-column prop="beginYear09" align="center" :label="responseData.beginYear + '年'"></el-table-column>
          <el-table-column prop="endYear09" align="center" :label="responseData.endYear + '年'"></el-table-column>
          <el-table-column prop="x09" align="center" label="同比"></el-table-column>
          <el-table-column prop="y09" align="center" label="环比"></el-table-column>
        </el-table-column>
        <el-table-column label="十月" align="center">
          <el-table-column prop="beginYear10" align="center" :label="responseData.beginYear + '年'"></el-table-column>
          <el-table-column prop="endYear10" align="center" :label="responseData.endYear + '年'"></el-table-column>
          <el-table-column prop="x10" align="center" label="同比"></el-table-column>
          <el-table-column prop="y10" align="center" label="环比"></el-table-column>
        </el-table-column>
        <el-table-column label="十一月" align="center">
          <el-table-column prop="beginYear11" align="center" :label="responseData.beginYear + '年'"></el-table-column>
          <el-table-column prop="endYear11" align="center" :label="responseData.endYear + '年'"></el-table-column>
          <el-table-column prop="x11" align="center" label="同比"></el-table-column>
          <el-table-column prop="y11" align="center" label="环比"></el-table-column>
        </el-table-column>
        <el-table-column label="十二月" align="center">
          <el-table-column prop="beginYear12" align="center" :label="responseData.beginYear + '年'"></el-table-column>
          <el-table-column prop="endYear12" align="center" :label="responseData.endYear + '年'"></el-table-column>
          <el-table-column prop="x12" align="center" label="同比"></el-table-column>
          <el-table-column prop="y12" align="center" label="环比"></el-table-column>
        </el-table-column>
        <el-table-column fixed="right" label="合计" align="center">
          <el-table-column prop="sumBeginYear" align="center" :label="responseData.beginYear + '年'"></el-table-column>
          <el-table-column prop="sumEndYear" align="center" :label="responseData.endYear + '年'"></el-table-column>
          <el-table-column  prop="sumX" align="center" label="同比"></el-table-column>
        </el-table-column>
      </el-table>
    </div>

  </div>
</template>

<script>
import {GetOperationStatistics} from "@/api/operation/indexStatistics"

export default {
  name: 'targetTable',
  props: ['itemCode',],
  components: {},
  data() {
    return {
      tableData: [],
      responseData: {
        itemList: [],
        beginYear: '',
        endYear: '',
      },
      formQuery: {
        itemCode: '',
        beginDate: '',
        endDate: '',
      },
      myStyle: {
        tableWidth: '',
        tableHeight: '',
      }
    }
  },
  created() {
  },
  watch: {
    'itemCode': function (newValue, oldValue) {
      this.getOperationStatistics(newValue);
    },
  },
  mounted() {
    let height = window.innerHeight;
    this.myStyle = {
      tableHeight: (height - 130) + "px",
    }
  },
  methods: {
    getOperationStatistics(data) {
      if (this.itemCode) {
        const loading = this.$loading({
          lock: true,
          text: "请稍等,数据正在努力提取中(●" + "◡" + "●)",
          spinner: "el-icon-coffee-cup",
          background: "rgba(0, 0, 0, 0.7)",
        });
        this.formQuery.itemCode = this.itemCode;
        GetOperationStatistics(this.formQuery).then(res => {
          this.responseData = res.data;
        }).finally(t => {
          loading.close();
        })
      }
    }
  },
}
</script>

<style scoped lang="scss">
.table-home {

  .table-master {
    ::v-deep.el-table .cell {
      line-height: 23px;
      padding: 0;
    }

  }
}
</style>
<style scoped>
/deep/ .el-table--medium .el-table__cell {
  padding: 1px 0;
}
</style>
