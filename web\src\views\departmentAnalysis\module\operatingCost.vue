<template>
    <div class="operating-cost-container">
        <div class="cost-summary" v-if="tableData.length > 0">
            <div class="summary-item">
                <span class="summary-label">总成本：</span>
                <span class="summary-value">{{ formatCurrency(totalCost) }}</span>
            </div>
        </div>

        <el-table :data="tableData" border stripe
            :height="tableData.length > 0 ? null : 120"
            style="width: 100%;"
            :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold', fontSize: '12px', padding: '8px 0' }"
            :empty-text="treeDeptCode ? '暂无数据' : '请先选择科室'">
            <el-table-column prop="personnelCost" label="人员经费" align="right" width="90">
                <template slot-scope="scope">
                    <span class="cost-value">{{ formatCurrency(scope.row.personnelCost) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="materialCost" label="卫生材料费" align="right" width="100">
                <template slot-scope="scope">
                    <span class="cost-value">{{ formatCurrency(scope.row.materialCost) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="medicineCost" label="药品费" align="right" width="80">
                <template slot-scope="scope">
                    <span class="cost-value">{{ formatCurrency(scope.row.medicineCost) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="depreciationCost" label="固定资产折旧费" align="right" width="120">
                <template slot-scope="scope">
                    <span class="cost-value">{{ formatCurrency(scope.row.depreciationCost) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="amortizationCost" label="无形资产摊销费" align="right" width="120">
                <template slot-scope="scope">
                    <span class="cost-value">{{ formatCurrency(scope.row.amortizationCost) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="taxCost" label="费用性税金" align="right" width="90">
                <template slot-scope="scope">
                    <span class="cost-value">{{ formatCurrency(scope.row.taxCost) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="otherCost" label="其他费用" align="right" width="80">
                <template slot-scope="scope">
                    <span class="cost-value">{{ formatCurrency(scope.row.otherCost) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="totalCost" label="总成本" align="right" width="100" fixed="right">
                <template slot-scope="scope">
                    <span class="total-cost-value">{{ formatCurrency(scope.row.totalCost) }}</span>
                </template>
            </el-table-column>
        </el-table>

        <div class="table-footer" v-if="tableData.length > 0">
            <span class="data-count">共 {{ tableData.length }} 条记录</span>
            <el-button type="primary" size="mini" icon="el-icon-refresh" @click="refreshData" :loading="isLoading">
                刷新数据
            </el-button>
        </div>
    </div>
</template>

<script>
import { GetManagementCount } from "@/api/departmentAnalysis/managementCount";

export default {
    name: 'operatingCost',
    props: ['deptCode'],
    data() {
        return {
            formQuery: {
                deptCode: '',
                startYearMonth: '',
                endYearMonth: '',
            },
            tableData: [],
            responseData: null,
            isLoading: false
        }
    },

    computed: {
        totalCost() {
            if (this.tableData.length === 0) return 0;
            return this.tableData.reduce((sum, item) => sum + (item.totalCost || 0), 0);
        },
        treeDeptCode() {
            return this.deptCode;
        }
    },

    watch: {
        'deptCode': function (newValue) {
            this.getOperatingCost(newValue);
        },
    },

    methods: {
        getOperatingCost() {
            if (this.deptCode) {
                this.isLoading = true;
                const loading = this.$loading({
                    lock: true,
                    text: "请稍等,数据正在努力提取中(●" + "◡" + "●)",
                    spinner: "el-icon-coffee-cup",
                    background: "rgba(0, 0, 0, 0.7)",
                });
                this.formQuery.deptCode = this.deptCode;
                GetManagementCount(this.formQuery).then(res => {
                    console.log("运营成本数据:", res);
                    this.responseData = res.data;
                    // 这里需要根据实际API返回的数据结构来处理
                    // 如果API返回的数据结构不同，请根据实际情况调整
                    this.tableData = res.data || [];

                    // 临时演示数据（实际使用时请删除）
                    if (this.tableData.length === 0) {
                        this.tableData = [
                            {
                                personnelCost: 150000,
                                materialCost: 80000,
                                medicineCost: 120000,
                                depreciationCost: 25000,
                                amortizationCost: 15000,
                                taxCost: 8000,
                                otherCost: 12000,
                                totalCost: 410000
                            }
                        ];
                    }

                    if (this.tableData.length > 0) {
                        this.$message.success("数据加载成功");
                    }
                }).catch(error => {
                    console.error("获取运营成本数据失败:", error);
                    this.$message.error("获取数据失败，请稍后重试");
                    this.tableData = [];
                }).finally(() => {
                    this.isLoading = false;
                    loading.close();
                })
            } else {
                this.tableData = [];
            }
        },

        refreshData() {
            if (this.deptCode) {
                this.getOperatingCost();
            } else {
                this.$message.warning("请先选择科室");
            }
        },

        formatCurrency(value) {
            if (value === null || value === undefined || value === '') return '-';
            const num = parseFloat(value);
            if (isNaN(num)) return '-';
            return num.toLocaleString('zh-CN', {
                style: 'currency',
                currency: 'CNY',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }
    }
}
</script>

<style scoped lang="scss">
.operating-cost-container {
    display: flex;
    flex-direction: column;
    max-height: 180px;

    .cost-summary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 8px 15px;
        border-radius: 6px;
        margin-bottom: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        flex-shrink: 0;

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .summary-label {
                font-size: 14px;
                font-weight: 500;
            }

            .summary-value {
                font-size: 16px;
                font-weight: bold;
                color: #ffd700;
            }
        }
    }

    .cost-value {
        color: #606266;
        font-weight: 500;
        font-size: 12px;
    }

    .total-cost-value {
        color: #e6a23c;
        font-weight: bold;
        font-size: 12px;
    }

    .table-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        color: #909399;
        font-size: 12px;
        border-top: 1px solid #ebeef5;
        margin-top: 5px;
        flex-shrink: 0;

        .data-count {
            font-weight: 500;
        }
    }

    // 表格容器样式
    :deep(.el-table) {
        border-radius: 6px;
        overflow: hidden;
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
        font-size: 12px;

        .el-table__header-wrapper {
            .el-table__header {
                th {
                    background: #f8f9fa !important;
                    color: #495057 !important;
                    font-weight: 600;
                    border-bottom: 2px solid #dee2e6;
                }
            }
        }

        .el-table__body-wrapper {
            .el-table__body {
                tr {
                    &:hover {
                        background-color: #f8f9fa !important;
                    }

                    td {
                        border-bottom: 1px solid #ebeef5;
                        padding: 8px 0;
                        font-size: 12px;
                    }
                }
            }
        }

        .el-table__empty-block {
            background: #fafafa;
            color: #909399;
            padding: 40px 0;
        }
    }
}
</style>