<template>
    <div>
        <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="date" label="人员经费" />
            <el-table-column prop="name" label="卫生材料费" />
            <el-table-column prop="address" label="药品费" />
            <el-table-column prop="date" label="固定资产折旧费" />
            <el-table-column prop="name" label="无形资产摊销费" />
            <el-table-column prop="address" label="费用性税金" />
            <el-table-column prop="name" label="其他费用" />
            <el-table-column prop="address" label="总成本" />
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'operatingCost',
    props: ['deptCode'],
    data() {
        return {
            formQuery: {
                deptCode: '',
                startYearMonth: '',
                endYearMonth: '',
            },
            tableData: []
        }
    },

    watch: {
        'deptCode': function (newValue, oldValue) {
            this.getOperatingCost(newValue);
        },
    },

    methods: {
        getOperatingCost(data) {
            if (this.deptCode) {
                const loading = this.$loading({
                    lock: true,
                    text: "请稍等,数据正在努力提取中(●" + "◡" + "●)",
                    spinner: "el-icon-coffee-cup",
                    background: "rgba(0, 0, 0, 0.7)",
                });
                this.formQuery.deptCode = this.deptCode;
                GetManagementCount(this.formQuery).then(res => {
                    console.log("1", res);
                    this.responseData = res.data;
                }).finally(t => {
                    loading.close();
                })
            }
        }
    }
}
</script>