import request from "@/utils/request";
const baseURL = "dict/";

// 查询字典类型列表
export function listType(query) {
  return request({
    url: baseURL + "listType",
    method: "get",
    params: query,
  });
}

// 查询字典类型详细
export function getType(dictId) {
  return request({
    url: baseURL + "getType?dictId=" + dictId,
    method: "get",
  });
}

// 新增字典类型
export function addType(data) {
  return request({
    url: baseURL + "addType",
    method: "post",
    data: data,
  });
}

// 修改字典类型
export function updateType(data) {
  return request({
    url: baseURL + "updateType",
    method: "put",
    data: data,
  });
}

// 删除字典类型
export function delType(dictId) {
  return request({
    url: baseURL + "delType?dictId=" + dictId,
    method: "delete",
  });
}

// 刷新字典缓存
export function refreshCache() {
  return request({
    url: baseURL + "ClearCache",
    method: "delete",
  });
}

// 获取字典选择框列表
export function optionselect() {
  return request({
    url: "/system/dict/type/optionselect",
    method: "get",
  });
}
