import request from '@/utils/request'

//查询科室名称
export function GetDeptList(query) {
  return request({
    url: '/DepOperManage/GetDeptList',
    method: 'get',
    params: query
  })
}


//查询科室数据
export function GetDepOperManageData(query) {
  return request({
    url: '/DepOperManage/GetDepOperManageData',
    method: 'get',
    params: query
  })
}

//查询科室对应数据
export function GetDeptData(query) {
  return request({
    url: '/DepOperManage/GetDeptData',
    method: 'get',
    params: query
  })
}


//保存
export function AddDepOperManage(query) {
  return request({
    url: '/DepOperManage/AddDepOperManage',
    method: 'get',
    params: query
  })
}
