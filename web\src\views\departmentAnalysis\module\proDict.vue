<template>
  <div class="dict-home" style="">
    <el-tree :data="dictTree" :props="defaultProps" @node-click="handleNodeClick" ></el-tree>
  </div>
</template>

<script>
import {GetDepartmentTree} from "@/api/departmentAnalysis/managementCount";

export default {
  name: 'targetDict',
  props: [],
  components: {},
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      dictTree: [],
    }
  },
  created() {
    const loading = this.$loading({
      lock: true,
      text: "请稍等,数据正在初始化中(●" + "◡" + "●)",
      spinner: "el-icon-coffee-cup",
      background: "rgba(0, 0, 0, 0.7)",
    });
    this.getOperationTree();
    loading.close()
  },
  mounted() {
  },
  methods: {
    getOperationTree(){
      GetDepartmentTree().then(res => {
        this.dictTree = res.data;
      })
    },
    handleNodeClick(data) {
      this.$emit('three-click',data.value)
    },
  },
}
</script>

<style scoped lang="scss">
.dict-home {
  padding-right: 50px;
  height: 600px; /* 设置为你想要的高度 */
  overflow-y: auto; /* 启用垂直滚动条 */
}
</style>
