/*
 * FilePath     : \src\api\login.js
 * Author       : LYF
 * Date         : 2024-07-21 11:22
 * LastEditors  : LYF
 * LastEditTime : 2024-07-21 14:57
 * Description  :
 * CodeIterationRecord:
 */
import request from "@/utils/request";
const baseURL = "system";
// 登录方法
export function login(username, password, code, uuid) {
  const data = {
    username,
    password,
    code,
    uuid,
  };
  return request({
    url: baseURL + "/login",
    headers: {
      isToken: false,
      repeatSubmit: false,
    },
    method: "post",
    data: data,
  });
}

// 注册方法
export function register(data) {
  return request({
    url: baseURL + "/register",
    headers: {
      isToken: false,
    },
    method: "post",
    data: data,
  });
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: baseURL + "/getInfo",
    method: "get",
  });
}

// 退出方法
export function logout() {
  return request({
    url: baseURL + "/logout",
    method: "post",
  });
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: baseURL + "/captchaImage",
    headers: {
      isToken: false,
    },
    method: "get",
    timeout: 20000,
  });
}
