<template>
  <div></div>
</template>

<script>
export default {
  name: 'transferStatement',
  props: [],
  components: {},
  data() {
    return {}
  },
  created() {
    this.verify();
  },
  mounted() {
  },
  methods: {
    verify() {
      let token = this.$route.query && this.$route.query.token;
      let status = this.$route.query && this.$route.query.status;
      let user = this.$route.query && this.$route.query.user;
      let c = true;
      if (token !== 'sB5fA5kX4gG9oJ7dR2sE5wJ1xZ2aB8zY') {
        c = false;
      } else if (status !== '0') {
        c = false;
      } else if (user !== 'yP0zM7mZ1uH1jV1vF8yG0oV4hG5lB3kQ') {
        c = false;
      }
      if (c){
        localStorage.setItem('statementStatus', JSON.stringify(true));
        this.$router.push("/shiftReport/pc")
      }else{
        localStorage.setItem('statementStatus', JSON.stringify(false));
        this.$router.push("/404")
      }
    }
  },
}
</script>

<style scoped lang="scss">

</style>
