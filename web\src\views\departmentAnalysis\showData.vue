<template>
  <div class="con">
    <el-row>
      <el-container>
        <el-col :span="3">
          <el-table v-loading="loading" :data="deptList" height="1680">
            <div slot="empty" style="text-align: left;">
              <el-empty description="哎呀，暂无数据啊！"/>
            </div>
            <el-table-column :show-overflow-tooltip="true" label="科室名称" align="center" prop="depT_NAME">
              <template slot-scope="scope">
                <div @click="clickName(scope.row)">
                  <el-tag>
                    {{ scope.row.depT_NAME }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="21" >
          <el-table :data="showListData" border style="width: 100%;text-align: center">
            <el-table-column property="rpT_Y" label="年份" min-width="90px" align="center"/>
            <el-table-column property="rpT_M" label="月份" min-width="90px" align="center"/>
            <el-table-column property="mzrc" label="门诊人次" min-width="90px" align="center"/>
            <el-table-column property="jzrc" label="急诊人次" min-width="90px" align="center"/>
            <el-table-column property="bmmzrc" label="便民门诊人次" min-width="100px" align="center"/>
            <el-table-column property="ryrc" label="入院人次" min-width="100px" align="center"/>
            <el-table-column property="cyrc" label="出院人次" min-width="100px" align="center"/>
            <el-table-column property="wzrc" label="危重人次" min-width="100px" align="center"/>
            <el-table-column property="wzrs" label="危重日数" min-width="100px" align="center"/>
            <el-table-column property="pjzyr" label="平均住院日" min-width="100px" align="center"/>
            <el-table-column property="sstc" label="手术台次" min-width="100px" align="center"/>
            <el-table-column property="one" label="一级手术台次" min-width="100px" align="center"/>
            <el-table-column property="two" label="二级手术台次" min-width="100px" align="center"/>
            <el-table-column property="three" label="三级手术台次" min-width="100px" align="center"/>
            <el-table-column property="four" label="四级手术台次" min-width="100px" align="center"/>
            <el-table-column property="five" label="三四级手术台次" min-width="100px" align="center"/>
            <el-table-column property="six" label="三四级手术占比" min-width="100px" align="center"/>
            <el-table-column property="zsr" label="总收入" min-width="100px" align="center"/>
            <el-table-column property="mzsr" label="门诊收入" min-width="100px" align="center"/>
            <el-table-column property="zysr" label="住院收入" min-width="100px" align="center"/>
            <el-table-column property="zyzb" label="总药占比" min-width="100px" align="center"/>
            <el-table-column property="zyyzb" label="住院药占比" min-width="100px" align="center"/>
            <el-table-column property="mzyzb" label="门诊药占比" min-width="100px" align="center"/>
            <el-table-column property="zyhczb" label="住院耗材占比" min-width="100px" align="center"/>
            <el-table-column property="zyjczb" label="住院检查占比" min-width="100px" align="center"/>
            <el-table-column property="zyjyzb" label="住院检验占比" min-width="100px" align="center"/>
            <el-table-column property="ylsr" label="医疗收入" min-width="100px" align="center"/>
            <el-table-column property="ylsrzb" label="医疗收入占比" min-width="100px" align="center"/>
            <el-table-column property="mzylsr" label="门诊医疗收入" min-width="100px" align="center"/>
            <el-table-column property="mzylsrzb" label="门诊医疗收入占比" min-width="100px" align="center"/>
            <el-table-column property="zyylsr" label="住院医疗收入" min-width="100px" align="center"/>
            <el-table-column property="zyylsrzb" label="住院医疗收入占比" min-width="100px" align="center"/>
            <el-table-column property="mzcjfy" label="门诊次均费用" min-width="100px" align="center"/>
            <el-table-column property="zycjfy" label="住院次均费用" min-width="100px" align="center"/>
            <el-table-column property="rlcb" label="人力成本" min-width="100px" align="center"/>
            <el-table-column property="ypf" label="药品费" min-width="100px" align="center"/>
            <el-table-column property="wscl" label="卫生材料成本" min-width="100px" align="center"/>
            <el-table-column property="gdzczj" label="固定资产折旧" min-width="100px" align="center"/>
            <el-table-column property="wxzctx" label="无形资产摊销" min-width="100px" align="center"/>
            <el-table-column property="qtfy" label="其他费用" min-width="100px" align="center"/>
            <el-table-column property="sdf" label="水电费" min-width="100px" align="center"/>
            <el-table-column property="nqf" label="暖气费" min-width="100px" align="center"/>
            <el-table-column property="bjf" label="保洁费" min-width="100px" align="center"/>
            <el-table-column property="gycb" label="公用成本" min-width="100px" align="center"/>
            <el-table-column property="glcb" label="管理成本" min-width="100px" align="center"/>
            <el-table-column property="yfcb" label="医辅成本" min-width="100px" align="center"/>
            <el-table-column property="yjcb" label="医技成本" min-width="100px" align="center"/>
          </el-table>
        </el-col>
      </el-container>
    </el-row>
  </div>
</template>

<script>
import {GetDeptDict, GetShowListData} from "@/api/departmentAnalysis/showData";

export default {
  name: 'showData',
  props: [],
  components: {},
  data() {
    return {
      loading: false,
      deptList: [],
      formParams: {
        deptName: '',
      },
      showListData: [],
    }
  },
  created() {
    this.getList()
  },
  mounted() {
  },
  methods: {
    // 获取科室操作数据
    getList() {
      const loading = this.$loading({
        lock: true,
        text: "加载中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      GetDeptDict().then(res => {
        this.deptList = res.data
        loading.close();
      })
    },
    //点击科室名称 展示对应记录的科室填写数据
    clickName(row) {
      this.formParams.deptName = row.depT_NAME
      this.getDetail(this.formParams)
    },
    // 获取科室操作数据
    getDetail(detailparam) {
      GetShowListData(detailparam).then((res) => {
        this.showListData = res.data
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.con {
  padding: 20px;
}

.el-table {
  max-height: 680px;
}
</style>

