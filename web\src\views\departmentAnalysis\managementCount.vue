<template>
  <div class="index-home" :style="this.masterStyle.home">
    <div class="title-master">
      科室运营管理分析
    </div>
    <div class="index-master">
      <div class="dict-master">
        <div class="dict-title">科室选项</div>
        <div class="dict">
          <pro-dict @three-click="monitorTree"></pro-dict>
        </div>
      </div>

      <div class="table" :style="tableWidth">
        <div class="table-master">
          <div class="table-title">
            项目统计数值
          </div>
          <pro-table :dept-code="treeDeptCode"></pro-table>
        </div>
        <div class="operating-cost-master">
          <div class="operating-cost-title">
            运营成本分析
          </div>
          <div class="operating-cost-content">
            <operating-cost :dept-code="treeDeptCode"></operating-cost>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import proDict from "@/views/departmentAnalysis/module/proDict";
import proTable from "@/views/departmentAnalysis/module/proTable";
import operatingCost from "@/views/departmentAnalysis/module/operatingCost";

export default {
  name: 'managementCount',
  props: [],
  components: {
    proDict, proTable, operatingCost
  },
  data() {
    return {
      windowWidth: 0,
      windowHeight: 0,
      windowSize: {
        width: 0,
        height: 0
      },
      masterStyle: {
        home: '',
      },
      tableWidth: '',
      treeDeptCode: '',
    }
  },
  created() {
    this.handleResize();
  },
  mounted() {
    window.addEventListener('resize', this.handleResize);
  },
  methods: {
    handleResize() {
      // 直接更新windowSize对象
      // 更新响应式数据，如果需要的话
      let width = window.innerWidth;
      let height = window.innerHeight;
      this.windowWidth = width;
      this.windowHeight = height;
      this.masterStyle.home = "height: " + (height - 40) + "px;width: " + width + "px;"
      this.tableWidth = "width:" + (width - 180) + "px"
    },
    monitorTree(data) {
      //得到统计指标代码
      this.treeDeptCode = data;
    },
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  }
}
</script>

<style scoped lang="scss">
.index-home {
  padding: 10px;

  .title-master {
    background: #3A71A8;
    color: #FFFFFF;
    height: 40px;
    font-size: 24px;
    border-radius: 5px 5px 0 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .index-master {
    display: flex;
    text-align: center;
    border: 1px solid #3A71A8;
    border-radius: 0 0 5px 5px;
    box-shadow: 5px 5px 10px #3A71A8;
    height: calc(100% - 40px);
    width: 100%;

    .dict-master {
      width: 180px;
      display: flex;
      flex-direction: column;

      .dict-title {
        height: 25px;
        font-size: 18px;
        background: #1c84c6;
        color: #FFFFFF;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        flex-shrink: 0;
      }

      .dict {
        flex: 1;
        border-right: 1px solid #3A71A8;
        overflow: auto;
      }
    }

    .table-master {
      flex: 4;
      display: flex;
      flex-direction: column;
      margin-right: 0;

      .table-title {
        height: 25px;
        font-size: 18px;
        background: #1c84c6;
        color: #FFFFFF;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
      }
    }

    .operating-cost-master {
      flex: 1;
      display: flex;
      flex-direction: column;

      .operating-cost-title {
        height: 25px;
        font-size: 18px;
        background: #1c84c6;
        color: #FFFFFF;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        white-space: nowrap;
        overflow: hidden;
      }

      .operating-cost-content {
        flex: 1;
        padding: 10px;
        background: #f8f9fa;
        border: 1px solid #3A71A8;
        border-top: none;
        overflow: auto;
      }
    }

    .table {
      display: flex;
      flex-direction: column;
      flex: 1;
      height: 100%;
      gap: 15px;
    }
  }

}
</style>
