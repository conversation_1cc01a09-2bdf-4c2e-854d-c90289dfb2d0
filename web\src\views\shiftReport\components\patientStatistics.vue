<template>
  <div class="patient-home-my">
    <div :style="myStyle.title">
      {{ this.title }}
    </div>
    <div :style="myStyle.height" class="table-d">
      <table>
        <!-- 设置列宽 -->
        <colgroup>
          <col width="5%">
          <col width="35%">
          <col width="20%">
          <col width="20%">
          <col width="20%">
          <col/>
        </colgroup>
        <thead>
        <th>序号</th>
        <th>科室名称</th>
        <th>在院</th>
        <th>病重</th>
        <th>病危</th>
        </thead>
      </table>
      <vue-seamless-scroll :data="tableData" :class-option="defaultOption" class="seamless-warp"
                           v-if="tableData[0]" :style="myStyle.patientTableHeight">
        <ul>
          <li v-for="(item,index) in tableData" class="liStyle">
            <span class="title1 text_align">{{ index + 1 }}</span>
            <span class="title2 text_align">{{ item.depT_NAME }}</span>
            <span class="title3 text_align">{{ item.zy }}</span>
            <span v-if="item.bz === 0" class="title3 text_align">{{ item.bz }}</span>
            <span v-else style="color: red" class="title3 text_align">{{ item.bz }}</span>
            <span v-if="item.bw === 0" class="title3 text_align">{{ item.bw }}</span>
            <span v-else style="color: red" class="title3 text_align">{{ item.bw }}</span>
          </li>
        </ul>
      </vue-seamless-scroll>
      <div class="seamless-warp">
        <ul>
          <li class="liStyle">
            <span class="title1 text_align"></span>
            <span class="title2 text_align">{{ sumData[0].summarizing }}</span>
            <span class="title3 text_align">{{ sumData[0].zy }}</span>
            <span class="title3 text_align">{{ sumData[0].bz }}</span>
            <span class="title3 text_align">{{ sumData[0].bw }}</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import {GetPatientStatementData} from '@/api/shiftReport/statement'
import vueSeamlessScroll from "vue-seamless-scroll";

export default {
  name: 'patientStatistics',
  props: ['title', 'myStyle'],
  components: {vueSeamlessScroll},
  computed: {
    defaultOption() {
      return {
        step: 0.1, // 数值越大速度滚动越快
        limitMoveNum: this.tableData.length, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      };
    },
  },
  data() {
    return {
      tableData: [],
      sumData: {},
    }
  },
  created() {
    this.getData()
  },
  mounted() {
  },
  methods: {
    getData() {
      GetPatientStatementData().then(res => {
        this.tableData = res.data.table;
        this.sumData = res.data.sum;
        console.log(this.sumData)
      })
    },
  },
}
</script>

<style scoped lang="scss">
.patient-home-my {
  color: #FFFFFF;

  * {
    margin: 0;
    padding: 0;
    /*font-weight: normal;*/
  }

  ul {
    list-style: none;
  }

  table {
    width: 100%;
    text-align: center;
  }

  th {
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    background-color: rgba(180, 181, 198, 0.1);
  }

  .seamless-warp {
    overflow: hidden;

    .liStyle {
      height: 50px;
      line-height: 30px;
      font-size: 26px;
      width: 100%;
      display: flex; //设置flex布局，否则span标签无法设置宽度
      //设置宽度与表头列宽一致
      .title1 {
        width: 5%;
      }

      .title2 {
        width: 35%;
      }

      .title3 {
        width: 20%;
      }

      .title4 {
        width: 30%;
      }

      .text_align {
        text-align: center;
      }
    }
  }

  border-bottom: 2px solid #262D5B;
}
</style>
