<template>
  <div class="dict-home">
    <el-tree :data="dictTree" :props="defaultProps" @node-click="handleNodeClick">

    </el-tree>
  </div>
</template>

<script>
import {GetOperationTree} from "@/api/operation/indexStatistics"
export default {
  name: 'targetDict',
  props: [],
  components: {},
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      dictTree:[]
    }
  },
  created() {
    const loading = this.$loading({
      lock: true,
      text: "请稍等,数据正在初始化中(●" + "◡" + "●)",
      spinner: "el-icon-coffee-cup",
      background: "rgba(0, 0, 0, 0.7)",
    });
    this.getOperationTree();
    loading.close()
  },
  methods: {
    getOperationTree(){
      GetOperationTree().then(res => {
        this.dictTree = res.data;
      })
    },
    handleNodeClick(data){
      this.$emit('three-click',data.value)
    },
  },
}
</script>

<style scoped lang="scss">
.dict-home{
  padding-right: 50px;

}
</style>
