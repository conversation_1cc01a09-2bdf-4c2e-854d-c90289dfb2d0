<template>
  <div class="statement-home" :style="myStyle.home">
    <div class="statement-title">
      河南宏力医院总值班交班内容
    </div>
    <div class="table-master">
      <div class="table-item">
        <index-statistics title="交班指标统计" :my-style="myStyle.itemStyle"></index-statistics>
      </div>
      <div class="table-item">
        <patient-statistics title="病危、病重患者统计" :my-style="myStyle.itemStyle"></patient-statistics>
      </div>
      <div class="table-item">
        <shift-records-week-content title="交班事件列表" :my-style="myStyle.itemStyle"></shift-records-week-content>
      </div>
    </div>
  </div>
</template>

<script>
import IndexStatistics from "@/views/shiftReport/components/IndexStatistics.vue";
import PatientStatistics from "@/views/shiftReport/components/patientStatistics.vue";
import ShiftRecordsWeekContent from "@/views/shiftReport/components/shiftRecordsWeekContent.vue";

export default {
  name: 'statement',
  props: [],
  components: {ShiftRecordsWeekContent, PatientStatistics, IndexStatistics},
  data() {
    return {
      myStyle: {
        home: '',
        itemStyle: {
          title: '',
          fontSize: '',
          tableHeight: '',
          height: '',
        },
      }
    }
  },
  created() {
    this.verify();
    this.createSize();
  },
  mounted() {
    window.addEventListener('resize', this.createSize)
  },
  methods: {
    createSize(){
      let width = window.innerWidth;
      let height = window.innerHeight;
      let itemHeight = height * 0.31 - 15;
      this.myStyle = {
        home: 'width: ' + width + "px;" + "height: " + height + "px;",
        itemStyle: {
          title: 'height: 45px;font-size: 28px;color: #FFFFFF;background-color: #161E4F;display: flex;justify-content: center;align-items: center;',
          fontSize: '22',
          tableHeight: "height: " + (itemHeight - 80) + "px !important;",
          height: "height: " + (itemHeight - 30) + "px !important;",
          patientTableHeight: "height: " + (itemHeight - 130) + "px !important;"
        },
      }
    },
    verify(){
      let status = JSON.parse(localStorage.getItem("statementStatus"));
      if (!status){
        this.$router.push("/404")
      }
    },
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.createSize)
  }
}
</script>

<style scoped lang="scss">
.statement-home{
  background-color: #161E4F;
  overflow-y: hidden;

  .statement-title{
    font-size: 32px;
    height: 55px;
    color: #FFFFFF;
    background-color: navy;
    display: flex;
    justify-content: center;
    align-items: center;

  }

  .table-master{
    display: flex;
    flex-direction: column;
    .table-item{

    }
  }

}
</style>
