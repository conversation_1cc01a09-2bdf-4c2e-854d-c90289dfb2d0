<template>
  <div class="index-home" :style="this.masterStyle.home">
    <div class="title-master">
      运营指标统计
    </div>
    <div class="index-master">
      <div class="dict-master">
        <div class="dict-title">指标选项</div>
        <div class="dict">
          <target-dict @three-click="monitorTree"></target-dict>
        </div>
      </div>

      <div class="table" :style="tableWidth">
        <div class="table-master" >
          <div class="table-title">
            指标统计数值
          </div>
        <target-table :item-code="treeItemCode"></target-table>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import TargetDict from "@/views/operation/module/targetDict.vue";
import TargetTable from "@/views/operation/module/targetTable.vue";

export default {
  name: 'IndexStatistics',
  props: [],
  components: {TargetTable, TargetDict},
  data() {
    return {
      windowWidth: 0,
      windowHeight: 0,
      windowSize: {
        width: 0,
        height: 0
      },
      masterStyle: {
        home: 'width: 100%',
      },
      tableWidth: '',
      treeItemCode: '',
    }
  },
  created() {
    this.handleResize();
  },
  mounted() {
    window.addEventListener('resize', this.handleResize);
  },
  methods: {
    handleResize() {
      // 直接更新windowSize对象
      // 更新响应式数据，如果需要的话
      let width = window.innerWidth;
      let height = window.innerHeight;
      this.windowWidth = width;
      this.windowHeight = height;
      this.masterStyle.home = "height: " + (height - 40) + "px;width: " + width + "px;"
      this.tableWidth =  "width:" + (width - 180) + "px",
      console.log(width)
    },
    monitorTree(data){
      this.treeItemCode = data;
    },
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  }
}
</script>

<style scoped lang="scss">
.index-home {
  padding: 10px;
  .title-master {
    background: #3A71A8;
    color: #FFFFFF;
    height: 40px;
    font-size: 24px;
    border-radius: 5px 5px 0 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .index-master {
    display: flex;
    border: 1px solid #3A71A8;
    border-radius: 0 0 5px 5px;
    box-shadow: 5px 5px 10px #3A71A8;
    height: 100%;
    .dict-master {
      .dict-title {
        height: 25px;
        width: 173px;
        font-size: 18px;
        background: #1c84c6;
        color: #FFFFFF;
        display: flex;
        justify-content: center;
      }
      .dict {
        height: 97.2%;
        border-right: 1px solid #3A71A8;
      }
    }

    .table-master{
      .table-title{
        height: 25px;
        font-size: 18px;
        background: #1c84c6;
        color: #FFFFFF;
        display: flex;
        justify-content: center;
      }
    }


    .table {
    }
  }

}
</style>
