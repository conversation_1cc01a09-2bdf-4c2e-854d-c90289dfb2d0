<template>
  <div class="con">
    <el-row>
      <el-container>
        <el-col :span="3">
          <div>
            <el-input v-model="query.deptName" placeholder="请输入部门名称" clearable size="small" prefix-icon="el-icon-search"
                      style="margin-bottom: 20px" @keyup.enter.native="getDept"/>
          </div>
          <el-table v-loading="loading" :data="deptList" height="1680">
            <div slot="empty" style="text-align: left;">
              <el-empty description="哎呀，暂无数据啊！"/>
            </div>
            <el-table-column :show-overflow-tooltip="true" label="科室名称" align="center" prop="dept_name">
              <template slot-scope="scope">
                <div @click="clickName(scope.row)">
                  <el-tag>
                    {{ scope.row.dept_name }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="20" :offset="1">
          <el-form :model="formParams" ref="queryForm" size="small" :inline="true">
            <el-form-item label="年份:">
              <el-input v-model="formParams.Year" placeholder="请输入年份" clearable size="small"
                        prefix-icon="el-icon-search"
                        style="margin-bottom: 20px;width: 140px" @keyup.enter.native="getDept"/>
            </el-form-item>
            <el-form-item label="月份:">
              <el-select v-model="formParams.selectedMonth" placeholder="请选择月份">
                <el-option
                  v-for="month in months"
                  :key="month"
                  :label="month"
                  :value="month"
                  style="width: 140px"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="getNeedList">查询</el-button>
            </el-form-item>
          </el-form>
          <!--     表格数据     -->
          <table border="1px" width="100%" cellspacing="0px" cellpadding="6px" align="center">
            <tr>
              <td>
                <div style="padding: 10px">
                  <div style="margin-top: 5px">
                    <span>门诊人次：</span>
                    <input v-model="formParams.mzrc" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">急诊人次：</span>
                    <input v-model="formParams.jzrc" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">便民门诊人次：</span>
                    <input v-model="formParams.bmmzrc" type="text" class="unit_input3"/>
                  </div>
                  <div style="margin-top: 5px">
                    <span>入院人次：</span>
                    <input v-model="formParams.ryrc" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">出院人次：</span>
                    <input v-model="formParams.cyrc" type="text" class="unit_input3"/>
                  </div>
                  <div style="margin-top: 5px">
                    <span>危重人次：</span>
                    <input v-model="formParams.wzrc" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">危重日数：</span>
                    <input v-model="formParams.wzrs" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">平均住院日：</span>
                    <input v-model="formParams.pjzyr" type="text" class="unit_input3"/>
                  </div>
                  <div style="margin-top: 5px">
                    <span>手术台次：</span>
                    <input v-model="formParams.sstc" type="text" class="unit_input2"/>
                    <span style="margin-left: 20px">一级手术台次：</span>
                    <input v-model="formParams.one" type="text" class="unit_input2"/>
                    <span style="margin-left: 20px">二级手术台次：</span>
                    <input v-model="formParams.two" type="text" class="unit_input2"/>
                    <span style="margin-left: 20px">三级手术台次：</span>
                    <input v-model="formParams.three" type="text" class="unit_input2"/>
                    <span style="margin-left: 20px">四级手术台次：</span>
                    <input v-model="formParams.four" type="text" class="unit_input2"/>
                    <div>
                      <span>三四级手术台次：</span>
                      <input v-model="formParams.five" type="text" class="unit_input2"/>
                      <span style="margin-left: 20px">三四级手术占比：</span>
                      <input v-model="formParams.six" type="text" class="unit_input2"/>
                    </div>

                  </div>
                  <div style="margin-top: 5px">
                    <span>总收入：</span>
                    <input v-model="formParams.zsr" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">门诊收入：</span>
                    <input v-model="formParams.mzsr" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">住院收入：</span>
                    <input v-model="formParams.zysr" type="text" class="unit_input3"/>
                  </div>
                  <div style="margin-top: 5px">
                    <span>总药占比：</span>
                    <input v-model="formParams.zyzb" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">住院药占比：</span>
                    <input v-model="formParams.zyyzb" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">门诊药占比：</span>
                    <input v-model="formParams.mzyzb" type="text" class="unit_input3"/>
                  </div>
                  <div style="margin-top: 5px">
                    <span>住院耗材占比：</span>
                    <input v-model="formParams.zyhczb" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">住院检查占比：</span>
                    <input v-model="formParams.zyjczb" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">住院检验占比：</span>
                    <input v-model="formParams.zyjyzb" type="text" class="unit_input3"/>
                  </div>
                  <div style="margin-top: 5px">
                    <span>医疗收入：</span>
                    <input v-model="formParams.ylsr" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">医疗收入占比：</span>
                    <input v-model="formParams.ylsrzb" type="text" class="unit_input3"/>
                  </div>
                  <div style="margin-top: 5px">
                    <span>门诊医疗收入：</span>
                    <input v-model="formParams.mzylsr" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">门诊医疗收入占比：</span>
                    <input v-model="formParams.mzylsrzb" type="text" class="unit_input3"/>
                  </div>
                  <div style="margin-top: 5px">
                    <span>住院医疗收入：</span>
                    <input v-model="formParams.zyylsr" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">住院医疗收入占比：</span>
                    <input v-model="formParams.zyylsrzb" type="text" class="unit_input3"/>
                  </div>
                  <div style="margin-top: 5px">
                    <span>门诊次均费用：</span>
                    <input v-model="formParams.mzcjfy" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">住院次均费用：</span>
                    <input v-model="formParams.zycjfy" type="text" class="unit_input3"/>
                  </div>
                  <div style="margin-top: 5px">
                    <span>人力成本：</span>
                    <input v-model="formParams.rlcb" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">药品费：</span>
                    <input v-model="formParams.ypf" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">卫生材料成本：</span>
                    <input v-model="formParams.wscl" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">固定资产折旧：</span>
                    <input v-model="formParams.gdzczj" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">无形资产摊销：</span>
                    <input v-model="formParams.wxzctx" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">其他费用：</span>
                    <input v-model="formParams.qtfy" type="text" class="unit_input3"/>
                  </div>
                  <div style="margin-top: 5px">
                    <span>水电费：</span>
                    <input v-model="formParams.sdf" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">暖气费：</span>
                    <input v-model="formParams.nqf" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">保洁费：</span>
                    <input v-model="formParams.bjf" type="text" class="unit_input3"/>
                  </div>
                  <div style="margin-top: 5px">
                    <span>公用成本：</span>
                    <input v-model="formParams.gycb" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">管理成本：</span>
                    <input v-model="formParams.glcb" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">医辅成本：</span>
                    <input v-model="formParams.yfcb" type="text" class="unit_input3"/>
                    <span style="margin-left: 20px">医技成本：</span>
                    <input v-model="formParams.yjcb" type="text" class="unit_input3"/>
                  </div>
                </div>
              </td>
            </tr>
            <tr style="height: 80px">
              <td>
                <el-button type="primary" size="small" @click="saveCard">保存</el-button>
              </td>
            </tr>
          </table>
        </el-col>
      </el-container>
    </el-row>
  </div>
</template>

<script>
import {AddDepOperManage, GetDepOperManageData, GetDeptData, GetDeptList} from "@/api/departmentAnalysis/depOperManage";

export default {
  name: 'depOperManage',
  props: [],
  components: {},
  data() {
    return {
      months: ['01月', '02月', '03月', '04月', '05月', '06月', '07月', '08月', '09月', '10月', '11月', '12月'],
      query: {
        deptName: '',
      },
      loading: false,
      deptList: [],//科室列表
      detailParam: [],
      formParams: {
        deptCode: '',
        deptName: '',
        Year: '',
        selectedMonth: '', // 选中月份的值
        mzrc: '',
        jzrc: '',
        bmmzrc: '',
        ryrc: '',
        cyrc: '',
        wzrc: '',
        wzrs: '',
        pjzyr: '',
        sstc: '',
        one: '',
        two: '',
        three: '',
        four: '',
        five: '',
        six: '',
        zsr: '',
        mzsr: '',
        zysr: '',
        zyzb: '',
        zyyzb: '',
        mzyzb: '',
        zyhczb: '',
        zyjczb: '',
        zyjyzb: '',
        ylsr: '',
        ylsrzb: '',
        mzylsr: '',
        mzylsrzb: '',
        zyylsr: '',
        zyylsrzb: '',
        mzcjfy: '',
        zycjfy: '',
        rlcb: '',
        ypf: '',
        wscl: '',
        gdzczj: '',
        wxzctx: '',
        qtfy: '',
        sdf: '',
        nqf: '',
        bjf: '',
        gycb: '',
        glcb: '',
        yfcb: '',
        yjcb: '',
      },
    }
  },
  created() {
    this.getDept()
  },
  mounted() {
  },
  methods: {
    // 获取科室列表
    getDept() {
      const loading = this.$loading({
        lock: true,
        text: "加载中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      GetDeptList(this.query).then(res => {
        this.deptList = res.data
        loading.close();
      })
    },
    //点击科室名称 展示对应记录的科室填写数据
    clickName(row) {
      this.formParams.deptName = row.dept_name
      this.formParams.deptCode = row.depT_CODE
      this.formParams.Year = new Date().getFullYear();
      var currentMonth = new Date().getMonth(); // 注意：返回值是从0开始的，所以实际月份需要加1
      currentMonth += 1 + "月"
      this.formParams.selectedMonth = currentMonth
      this.getDetail(this.formParams)
    },

    //获取详情
    getDetail(detailparam) {
      GetDeptData(detailparam).then((res) => {
        if (res.code == 200) {
          if (res.data.length == 0) {
            this.formParams.mzrc = ''
            this.formParams.jzrc = ''
            this.formParams.bmmzrc = ''
            this.formParams.ryrc = ''
            this.formParams.cyrc = ''
            this.formParams.wzrc = ''
            this.formParams.wzrs = ''
            this.formParams.pjzyr = ''
            this.formParams.sstc = ''
            this.formParams.one = ''
            this.formParams.two = ''
            this.formParams.three = ''
            this.formParams.four = ''
            this.formParams.five = ''
            this.formParams.six = ''
            this.formParams.zsr = ''
            this.formParams.mzsr = ''
            this.formParams.zysr = ''
            this.formParams.zyzb = ''
            this.formParams.zyyzb = ''
            this.formParams.mzyzb = ''
            this.formParams.zyhczb = ''
            this.formParams.zyjczb = ''
            this.formParams.zyjyzb = ''
            this.formParams.ylsr = ''
            this.formParams.ylsrzb = ''
            this.formParams.mzylsr = ''
            this.formParams.mzylsrzb = ''
            this.formParams.zyylsr = ''
            this.formParams.zyylsrzb = ''
            this.formParams.mzcjfy = ''
            this.formParams.zycjfy = ''
            this.formParams.rlcb = ''
            this.formParams.ypf = ''
            this.formParams.wscl = ''
            this.formParams.gdzczj = ''
            this.formParams.wxzctx = ''
            this.formParams.qtfy = ''
            this.formParams.sdf = ''
            this.formParams.nqf = ''
            this.formParams.bjf = ''
            this.formParams.gycb = ''
            this.formParams.glcb = ''
            this.formParams.yfcb = ''
            this.formParams.yjcb = ''
          } else {
            this.formParams.mzrc = res.data[0].mzrc == 0 ? '' : res.data[0].mzrc
            this.formParams.jzrc = res.data[0].jzrc == 0 ? '' : res.data[0].jzrc
            this.formParams.bmmzrc = res.data[0].bmmzrc == 0 ? '' : res.data[0].bmmzrc
            this.formParams.ryrc = res.data[0].ryrc == 0 ? '' : res.data[0].ryrc
            this.formParams.cyrc = res.data[0].cyrc == 0 ? '' : res.data[0].cyrc
            this.formParams.wzrc = res.data[0].wzrc == 0 ? '' : res.data[0].wzrc
            this.formParams.wzrs = res.data[0].wzrs == 0 ? '' : res.data[0].wzrs
            this.formParams.pjzyr = res.data[0].pjzyr == 0 ? '' : res.data[0].pjzyr
            this.formParams.sstc = res.data[0].sstc == 0 ? '' : res.data[0].sstc
            this.formParams.one = res.data[0].one == 0 ? '' : res.data[0].one
            this.formParams.two = res.data[0].two == 0 ? '' : res.data[0].two
            this.formParams.three = res.data[0].three == 0 ? '' : res.data[0].three
            this.formParams.four = res.data[0].four == 0 ? '' : res.data[0].four
            this.formParams.five = res.data[0].five == 0 ? '' : res.data[0].five
            this.formParams.six = res.data[0].six == 0 ? '' : res.data[0].six
            this.formParams.zsr = res.data[0].zsr == 0 ? '' : res.data[0].zsr
            this.formParams.mzsr = res.data[0].mzsr == 0 ? '' : res.data[0].mzsr
            this.formParams.zysr = res.data[0].zysr == 0 ? '' : res.data[0].zysr
            this.formParams.zyzb = res.data[0].zyzb == 0 ? '' : res.data[0].zyzb
            this.formParams.zyyzb = res.data[0].zyyzb == 0 ? '' : res.data[0].zyyzb
            this.formParams.mzyzb = res.data[0].mzyzb == 0 ? '' : res.data[0].mzyzb
            this.formParams.zyhczb = res.data[0].zyhczb == 0 ? '' : res.data[0].zyhczb
            this.formParams.zyjczb = res.data[0].zyjczb == 0 ? '' : res.data[0].zyjczb
            this.formParams.zyjyzb = res.data[0].zyjyzb == 0 ? '' : res.data[0].zyjyzb
            this.formParams.ylsr = res.data[0].ylsr == 0 ? '' : res.data[0].ylsr
            this.formParams.ylsrzb = res.data[0].ylsrzb == 0 ? '' : res.data[0].ylsrzb
            this.formParams.mzylsr = res.data[0].mzylsr == 0 ? '' : res.data[0].mzylsr
            this.formParams.mzylsrzb = res.data[0].mzylsrzb == 0 ? '' : res.data[0].mzylsrzb
            this.formParams.zyylsr = res.data[0].zyylsr == 0 ? '' : res.data[0].zyylsr
            this.formParams.zyylsrzb = res.data[0].zyylsrzb == 0 ? '' : res.data[0].zyylsrzb
            this.formParams.mzcjfy = res.data[0].mzcjfy == 0 ? '' : res.data[0].mzcjfy
            this.formParams.zycjfy = res.data[0].zycjfy == 0 ? '' : res.data[0].zycjfy
            this.formParams.rlcb = res.data[0].rlcb == 0 ? '' : res.data[0].rlcb
            this.formParams.ypf = res.data[0].ypf == 0 ? '' : res.data[0].ypf
            this.formParams.wscl = res.data[0].wscl == 0 ? '' : res.data[0].wscl
            this.formParams.gdzczj = res.data[0].gdzczj == 0 ? '' : res.data[0].gdzczj
            this.formParams.wxzctx = res.data[0].wxzctx == 0 ? '' : res.data[0].wxzctx
            this.formParams.qtfy = res.data[0].qtfy == 0 ? '' : res.data[0].qtfy
            this.formParams.sdf = res.data[0].sdf == 0 ? '' : res.data[0].sdf
            this.formParams.nqf = res.data[0].nqf == 0 ? '' : res.data[0].nqf
            this.formParams.bjf = res.data[0].bjf == 0 ? '' : res.data[0].bjf
            this.formParams.gycb = res.data[0].gycb == 0 ? '' : res.data[0].gycb
            this.formParams.glcb = res.data[0].glcb == 0 ? '' : res.data[0].glcb
            this.formParams.yfcb = res.data[0].yfcb == 0 ? '' : res.data[0].yfcb
            this.formParams.yjcb = res.data[0].yjcb == 0 ? '' : res.data[0].yjcb
          }
        }
      })
    },

    //条件查询
    getNeedList() {
      if (!this.formParams.deptName) {
        this.$message({
          showClose: true,
          message: '请点击选择科室',
          type: 'error',
        })
        return
      } else {
        GetDeptData(this.formParams).then((res) => {
          if (res.code == 200) {
            if (res.data.length == 0) {
              this.$message({
                message: '该科室未填写数据',
                type: 'warning',
                duration: 700
              })
              this.formParams.mzrc = ''
              this.formParams.jzrc = ''
              this.formParams.bmmzrc = ''
              this.formParams.ryrc = ''
              this.formParams.cyrc = ''
              this.formParams.wzrc = ''
              this.formParams.wzrs = ''
              this.formParams.pjzyr = ''
              this.formParams.sstc = ''
              this.formParams.one = ''
              this.formParams.two = ''
              this.formParams.three = ''
              this.formParams.four = ''
              this.formParams.five = ''
              this.formParams.six = ''
              this.formParams.zsr = ''
              this.formParams.mzsr = ''
              this.formParams.zysr = ''
              this.formParams.zyzb = ''
              this.formParams.zyyzb = ''
              this.formParams.mzyzb = ''
              this.formParams.zyhczb = ''
              this.formParams.zyjczb = ''
              this.formParams.zyjyzb = ''
              this.formParams.ylsr = ''
              this.formParams.ylsrzb = ''
              this.formParams.mzylsr = ''
              this.formParams.mzylsrzb = ''
              this.formParams.zyylsr = ''
              this.formParams.zyylsrzb = ''
              this.formParams.mzcjfy = ''
              this.formParams.zycjfy = ''
              this.formParams.rlcb = ''
              this.formParams.ypf = ''
              this.formParams.wscl = ''
              this.formParams.gdzczj = ''
              this.formParams.wxzctx = ''
              this.formParams.qtfy = ''
              this.formParams.sdf = ''
              this.formParams.nqf = ''
              this.formParams.bjf = ''
              this.formParams.gycb = ''
              this.formParams.glcb = ''
              this.formParams.yfcb = ''
              this.formParams.yjcb = ''
            } else {
              this.formParams.mzrc = res.data[0].mzrc == 0 ? '' : res.data[0].mzrc
              this.formParams.jzrc = res.data[0].jzrc == 0 ? '' : res.data[0].jzrc
              this.formParams.bmmzrc = res.data[0].bmmzrc == 0 ? '' : res.data[0].bmmzrc
              this.formParams.ryrc = res.data[0].ryrc == 0 ? '' : res.data[0].ryrc
              this.formParams.cyrc = res.data[0].cyrc == 0 ? '' : res.data[0].cyrc
              this.formParams.wzrc = res.data[0].wzrc == 0 ? '' : res.data[0].wzrc
              this.formParams.wzrs = res.data[0].wzrs == 0 ? '' : res.data[0].wzrs
              this.formParams.pjzyr = res.data[0].pjzyr == 0 ? '' : res.data[0].pjzyr
              this.formParams.sstc = res.data[0].sstc == 0 ? '' : res.data[0].sstc
              this.formParams.one = res.data[0].one == 0 ? '' : res.data[0].one
              this.formParams.two = res.data[0].two == 0 ? '' : res.data[0].two
              this.formParams.three = res.data[0].three == 0 ? '' : res.data[0].three
              this.formParams.four = res.data[0].four == 0 ? '' : res.data[0].four
              this.formParams.five = res.data[0].five == 0 ? '' : res.data[0].five
              this.formParams.six = res.data[0].six == 0 ? '' : res.data[0].six
              this.formParams.zsr = res.data[0].zsr == 0 ? '' : res.data[0].zsr
              this.formParams.mzsr = res.data[0].mzsr == 0 ? '' : res.data[0].mzsr
              this.formParams.zysr = res.data[0].zysr == 0 ? '' : res.data[0].zysr
              this.formParams.zyzb = res.data[0].zyzb == 0 ? '' : res.data[0].zyzb
              this.formParams.zyyzb = res.data[0].zyyzb == 0 ? '' : res.data[0].zyyzb
              this.formParams.mzyzb = res.data[0].mzyzb == 0 ? '' : res.data[0].mzyzb
              this.formParams.zyhczb = res.data[0].zyhczb == 0 ? '' : res.data[0].zyhczb
              this.formParams.zyjczb = res.data[0].zyjczb == 0 ? '' : res.data[0].zyjczb
              this.formParams.zyjyzb = res.data[0].zyjyzb == 0 ? '' : res.data[0].zyjyzb
              this.formParams.ylsr = res.data[0].ylsr == 0 ? '' : res.data[0].ylsr
              this.formParams.ylsrzb = res.data[0].ylsrzb == 0 ? '' : res.data[0].ylsrzb
              this.formParams.mzylsr = res.data[0].mzylsr == 0 ? '' : res.data[0].mzylsr
              this.formParams.mzylsrzb = res.data[0].mzylsrzb == 0 ? '' : res.data[0].mzylsrzb
              this.formParams.zyylsr = res.data[0].zyylsr == 0 ? '' : res.data[0].zyylsr
              this.formParams.zyylsrzb = res.data[0].zyylsrzb == 0 ? '' : res.data[0].zyylsrzb
              this.formParams.mzcjfy = res.data[0].mzcjfy == 0 ? '' : res.data[0].mzcjfy
              this.formParams.zycjfy = res.data[0].zycjfy == 0 ? '' : res.data[0].zycjfy
              this.formParams.rlcb = res.data[0].rlcb == 0 ? '' : res.data[0].rlcb
              this.formParams.ypf = res.data[0].ypf == 0 ? '' : res.data[0].ypf
              this.formParams.wscl = res.data[0].wscl == 0 ? '' : res.data[0].wscl
              this.formParams.gdzczj = res.data[0].gdzczj == 0 ? '' : res.data[0].gdzczj
              this.formParams.wxzctx = res.data[0].wxzctx == 0 ? '' : res.data[0].wxzctx
              this.formParams.qtfy = res.data[0].qtfy == 0 ? '' : res.data[0].qtfy
              this.formParams.sdf = res.data[0].sdf == 0 ? '' : res.data[0].sdf
              this.formParams.nqf = res.data[0].nqf == 0 ? '' : res.data[0].nqf
              this.formParams.bjf = res.data[0].bjf == 0 ? '' : res.data[0].bjf
              this.formParams.gycb = res.data[0].gycb == 0 ? '' : res.data[0].gycb
              this.formParams.glcb = res.data[0].glcb == 0 ? '' : res.data[0].glcb
              this.formParams.yfcb = res.data[0].yfcb == 0 ? '' : res.data[0].yfcb
              this.formParams.yjcb = res.data[0].yjcb == 0 ? '' : res.data[0].yjcb
            }
          }
        })
      }
    },

    //保存
    saveCard() {
      if (!this.formParams.deptName) {
        this.$message({
          showClose: true,
          message: '请点击选择科室',
          type: 'error',
        })
        return
      } else {
        AddDepOperManage(this.formParams).then(res => {
          this.$message({
            message: res.message,
            type: 'success',
          })
        })
      }

    },
  },
}
</script>

<style lang="scss" scoped>
.con {
  padding: 20px;
}

.el-table {
  max-height: 680px;
}

.unit_input1 {
  border: none;
  border-bottom: 1px solid rgb(0, 0, 0);
  outline: none;
  width: 30px;
  font-size: 12px;
  font-weight: bold;
  margin-left: 3px;
}

.unit_input2 {
  border: none;
  border-bottom: 1px solid rgb(0, 0, 0);
  outline: none;
  width: 60px;
  font-size: 12px;
  font-weight: bold;
  margin-left: 3px;
}

.unit_input3 {
  border: none;
  border-bottom: 1px solid rgb(0, 0, 0);
  outline: none;
  width: 80px;
  font-size: 12px;
  font-weight: bold;
  margin-left: 3px;
}

.span {
  display: inline-block;
}

.checkBoxMargin {
  margin: 0px 0;
}

.flex-container {
  display: flex;
  flex-wrap: wrap;

  .flex-item {
    width: 100%;
  }
}

</style>
